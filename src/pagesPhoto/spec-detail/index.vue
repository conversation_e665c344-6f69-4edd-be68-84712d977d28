<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "规格详情",
    "backgroundColor": "#fff"
  }
}
</route>

<script setup lang="ts">
import type { IPhotoItem } from '@/api/photo'
import { ref } from 'vue'

// 规格详情数据
const detail = ref<IPhotoItem>({
  id: 0,
  name: '默认规格',
  widthMm: 35,
  heightMm: 45,
  widthPx: 413,
  heightPx: 531,
  icon: 0,
  sort: 0,
  category: 1,
  dpi: 300,
})

// 拍摄指南图片列表
const guideImages = ref([
  '/static/icon/guide1.png',
  '/static/icon/guide2.png',
  '/static/icon/guide3.png',
  '/static/icon/guide4.png',
  '/static/icon/guide5.png',
])

// 美颜开关状态
const isBeautyOn = ref(false)

// 轮播图点击事件
function handleSwiperClick(_event: any) {
  // TODO: 实现轮播图点击逻辑
}

// 轮播图切换事件
function handleSwiperChange(_event: any) {
  // TODO: 实现轮播图切换逻辑
}

// 美颜开关切换
function onBeautySwitch(value: boolean) {
  isBeautyOn.value = value
  // TODO: 实现美颜开关逻辑
}

// 从相册选择
function chooseImage() {
  // TODO: 实现相册选择逻辑
}

// 拍摄照片
function chooseCamera() {
  // TODO: 实现拍摄逻辑
}

// 页面加载时获取详情数据
onLoad((options: any) => {
  // 从URL参数中获取传递的数据
  if (options.data) {
    try {
      const itemData = JSON.parse(decodeURIComponent(options.data)) as IPhotoItem
      detail.value = itemData
    }
    catch (error) {
      console.error('解析传递的数据失败:', error)
      uni.showToast({
        title: '数据解析失败',
        icon: 'none',
      })
    }
  }
})
</script>

<template>
  <view class="bg-white">
    <!-- 规格详情信息 -->
    <view class="m-[35rpx_35rpx_0_35rpx] rounded-[20rpx] bg-white p-[35rpx_0] shadow-[inset_-2rpx_-2rpx_4rpx_2rpx_rgba(0,0,0,0.02),inset_2rpx_2rpx_4rpx_2rpx_rgba(0,0,0,0.02)]">
      <view class="ml-[35rpx] text-[34rpx] font-bold leading-[34rpx]">
        {{ detail.name }}
      </view>
      <view class="ml-[35rpx] mt-[20rpx] flex flex-row text-[28rpx]">
        <view class="w-[188rpx] text-[#9b9b9b]">
          照片尺寸
        </view>
        <view class="w-full text-[#434343]">
          {{ detail.widthMm }}*{{ detail.heightMm }}mm
        </view>
      </view>
      <view class="ml-[35rpx] mt-[20rpx] flex flex-row text-[28rpx]">
        <view class="w-[188rpx] text-[#9b9b9b]">
          照片规格
        </view>
        <view class="w-full text-[#434343]">
          {{ detail.widthPx }}*{{ detail.heightPx }}px
        </view>
      </view>
      <view class="ml-[35rpx] mt-[20rpx] flex flex-row text-[28rpx]">
        <view class="w-[188rpx] text-[#9b9b9b]">
          照片底色
        </view>
        <view class="w-full flex flex-row text-[#434343]">
          <view class="color white" />
          <view class="color blue" />
          <view class="color red" />
          <view class="color rainbow" />
        </view>
      </view>
      <view class="ml-[35rpx] mt-[20rpx] flex flex-row text-[28rpx]">
        <view class="w-[188rpx] text-[#9b9b9b]">
          文件大小
        </view>
        <view class="w-full text-[#434343]">
          不限制
        </view>
      </view>
    </view>

    <!-- 拍摄指南 -->
    <view class="mt-[35rpx]">
      <view class="ml-[35rpx] flex items-center">
        <image src="/static/icon/guide_icon.png" class="h-[44rpx] w-[44rpx]" />
        <view class="ml-[26rpx] text-[34rpx] font-bold">
          拍摄指南
        </view>
      </view>

      <!-- 使用 wot-design-uni 的轮播组件 -->
      <wd-swiper
        :list="guideImages"
        :autoplay="true"
        :interval="4000"
        :indicator="false"
        custom-style="margin: 35rpx 35rpx 0rpx 35rpx;  height: 368rpx; border-radius: 20rpx;"
        @click="handleSwiperClick"
        @change="handleSwiperChange"
      />
    </view>

    <!-- 美颜开关 -->
    <view class="m-[30rpx_35rpx] flex items-center justify-between rounded-[20rpx] bg-white p-[20rpx_30rpx] shadow-[inset_-2rpx_-2rpx_4rpx_2rpx_rgba(0,0,0,0.02),inset_2rpx_2rpx_4rpx_2rpx_rgba(0,0,0,0.02)]">
      <text class="text-[34rpx] text-[#434343] font-bold">
        美颜
      </text>
      <wd-switch
        v-model="isBeautyOn"
        active-color="#8280FF"
        @change="onBeautySwitch"
      />
    </view>

    <!-- 底部按钮 -->
    <view class="fixed bottom-[30rpx] left-0 right-0 z-[999] flex justify-center p-[0_40rpx]">
      <wd-button
        plain
        custom-style="height: 88rpx; margin: 0 20rpx; width: 325rpx; border-radius: 100px; font-size: 34rpx; font-weight: 700; background: rgba(159, 159, 255, 0.3); color: #8280FF; border: none;"
        @click="chooseImage"
      >
        从相册中选择
      </wd-button>
      <wd-button
        custom-style="height: 88rpx; margin: 0 20rpx; width: 325rpx; border-radius: 100px; font-size: 34rpx; font-weight: 700; background: #8280FF; color: #FFFFFF; border: none;"
        @click="chooseCamera"
      >
        拍摄
      </wd-button>
    </view>
  </view>
</template>
